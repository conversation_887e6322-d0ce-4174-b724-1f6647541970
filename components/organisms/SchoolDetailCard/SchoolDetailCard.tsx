'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Mail, Phone, MapPin, Edit, UserCircle,
  Users, GraduationCap, ExternalLink, Calendar, Clock, Info, BookOpen,
  Award, Palette, Trash2, <PERSON><PERSON><PERSON>riangle,
} from 'lucide-react';
import { ISchoolResponse } from '@/apis/schoolApi';
import { Button } from '@/components/atoms/Button/Button';
import { getFileRenderUrl } from '@/utils/fileUtils';
import { EUserRole } from '@/config/enums/user';

export interface SchoolDetailCardProps {
  school: ISchoolResponse;
  onEdit?: (school: ISchoolResponse) => void;
  onDelete?: (schoolId: string) => Promise<void>;
  canDelete?: boolean;
  userRole?: EUserRole;
}

export const SchoolDetailCard: React.FC<SchoolDetailCardProps> = ({
  school,
  onEdit,
  onDelete,
  canDelete = false,
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(school);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (onDelete) {
      try {
        setIsDeleting(true);
        await onDelete(school.id);
        // The parent component should handle navigation after successful deletion
      } catch (error) {
        console.error('Error deleting school:', error);
        setIsDeleting(false);
        setShowDeleteConfirm(false);
      }
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // Format phone number for better readability
  const formatPhoneNumber = (phone: string) => {
    // Simple formatting - could be enhanced based on specific requirements
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  };

  return (
    <div className="relative">
      {/* Main Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-md">

        {/* Delete confirmation dialog */}
        {showDeleteConfirm && (
          <div className="absolute inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 border border-gray-200">
              <div className="mb-6">
                <div className="flex items-center text-red-600 bg-red-50 rounded-lg p-4">
                  <AlertTriangle size={20} className="mr-3" />
                  <h3 className="text-lg font-semibold">Delete Confirmation</h3>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <p className="text-gray-700">
                  Are you sure you want to delete <strong className="text-red-600">{school.name}</strong>?
                </p>
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                  <p className="text-amber-800 text-sm flex items-center gap-2">
                    <AlertTriangle size={16} className="text-amber-600" />
                    This action cannot be undone
                  </p>
                  <p className="text-amber-700 text-sm mt-1">
                    All data associated with this school will be permanently removed.
                  </p>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={handleCancelDelete}
                  disabled={isDeleting}
                  className="text-sm"
                >
                  Cancel
                </Button>
                <Button
                  variant="error"
                  onClick={handleConfirmDelete}
                  disabled={isDeleting}
                  className="text-sm flex items-center gap-2"
                >
                  {isDeleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Deleting...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 size={16} />
                      <span>Delete</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="bg-gray-50 border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Status Indicator */}
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="bg-green-50 px-3 py-1 rounded-full border border-green-200">
                  <span className="text-sm font-medium text-green-700">
                    Active School
                  </span>
                </div>
              </div>

              {/* Registration Number */}
              {school.registeredNumber && (
                <div className="bg-white px-3 py-1 rounded-full border border-gray-200">
                  <span className="text-sm text-gray-700">
                    <span className="font-medium text-blue-600">Reg:</span>
                    <span className="font-mono ml-1">{school.registeredNumber}</span>
                  </span>
                </div>
              )}
            </div>

            {/* Metadata Section */}
            <div className="flex items-center gap-4 text-xs text-gray-600">
              {school.createdAt && (
                <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full border border-gray-200">
                  <Calendar size={12} className="text-gray-500" />
                  <span>Est. {new Date(school.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}</span>
                </div>
              )}
              {school.updatedAt && school.updatedAt !== school.createdAt && (
                <div className="flex items-center gap-2 bg-white px-3 py-1 rounded-full border border-gray-200">
                  <Clock size={12} className="text-gray-500" />
                  <span>Updated {new Date(school.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}</span>
                </div>
              )}
            </div>
          </div>
        </div>

      <div className="p-6">
        {/* School Header */}
        <div className="flex flex-col gap-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* School Logo/Brand */}
              <div className="flex-shrink-0">
                {school.brand?.logo ? (
                  <div className="w-16 h-16 rounded-lg overflow-hidden border border-gray-200 bg-white shadow-sm">
                    <Image
                      src={getFileRenderUrl(school.brand.logo)}
                      alt={`${school.name} Logo`}
                      width={64}
                      height={64}
                      className="object-contain w-full h-full p-2"
                    />
                  </div>
                ) : (
                  <div
                    className="w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-xl shadow-sm"
                    style={{ backgroundColor: school.brand?.color || '#3B82F6' }}
                  >
                    <span>{school.name.charAt(0).toUpperCase()}</span>
                  </div>
                )}
              </div>

              {/* School Name */}
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-bold text-gray-800">{school.name}</h1>

                {/* Edit Button */}
                {onEdit ? (
                  <button
                    onClick={handleEditClick}
                    className="p-2 text-gray-500 hover:text-blue-600 bg-gray-50 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit School"
                  >
                    <Edit size={16} />
                  </button>
                ) : (
                  <Link
                    href="#"
                    className="p-2 text-gray-500 hover:text-blue-600 bg-gray-50 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit School"
                  >
                    <Edit size={16} />
                  </Link>
                )}
              </div>
            </div>

            {/* Delete Button */}
            {canDelete && onDelete && (
              <Button
                variant="error"
                onClick={handleDeleteClick}
                className="inline-flex items-center gap-2 text-sm"
                disabled={isDeleting || showDeleteConfirm}
              >
                <Trash2 size={16} />
                <span>Delete School</span>
              </Button>
            )}
          </div>
        </div>

        {/* Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Contact Information */}
          <div className="bg-gray-50 rounded-lg p-5 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Info size={18} className="text-gray-600" />
              <span>Contact Information</span>
            </h3>
            <div className="space-y-4">
              {school.phoneNumber && (
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <Phone size={16} className="text-gray-600 flex-shrink-0" />
                  <a
                    href={`tel:${school.phoneNumber}`}
                    className="text-sm text-gray-700 hover:text-blue-600 transition-colors flex-1"
                  >
                    {formatPhoneNumber(school.phoneNumber)}
                  </a>
                </div>
              )}
              {school.email && (
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <Mail size={16} className="text-gray-600 flex-shrink-0" />
                  <a
                    href={`mailto:${school.email}`}
                    className="text-sm text-gray-700 hover:text-blue-600 transition-colors break-all flex-1"
                  >
                    {school.email}
                  </a>
                </div>
              )}
              {school.address && (
                <div className="flex items-start gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <MapPin size={16} className="text-gray-600 flex-shrink-0 mt-0.5" />
                  <a
                    href={`https://maps.google.com/?q=${encodeURIComponent(school.address)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-700 hover:text-blue-600 transition-colors flex items-start gap-2 flex-1"
                  >
                    <span>{school.address}</span>
                    <ExternalLink size={12} className="opacity-60 flex-shrink-0 mt-0.5" />
                  </a>
                </div>
              )}
              {school.admin && (
                <div className="flex items-start gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <UserCircle size={16} className="text-gray-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-gray-700 flex-1">
                    <p className="font-semibold text-gray-800">{school.admin.name}</p>
                    {school.admin.email && (
                      <a
                        href={`mailto:${school.admin.email}`}
                        className="text-xs text-gray-600 hover:text-blue-600 transition-colors"
                      >
                        {school.admin.email}
                      </a>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Brand & Additional Info */}
          <div className="space-y-6">
            {/* Brand Information */}
            {school.brand && (
              <div className="bg-gray-50 rounded-lg p-5 border border-gray-200">
                <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                  <Palette size={18} className="text-gray-600" />
                  <span>Brand Identity</span>
                </h3>
                <div className="flex items-center gap-4 p-3 bg-white rounded-lg border border-gray-200">
                  {school.brand.color && (
                    <div
                      className="w-10 h-10 rounded-lg border border-gray-200 shadow-sm flex-shrink-0"
                      style={{ backgroundColor: school.brand.color }}
                      title={`Brand Color: ${school.brand.color}`}
                    />
                  )}
                  <div>
                    {school.brand.color && (
                      <div>
                        <p className="text-xs text-gray-500 font-medium">Primary Color</p>
                        <span className="text-sm font-mono font-semibold text-gray-700">
                          {school.brand.color}
                        </span>
                      </div>
                    )}
                  </div>
                  {school.brand.image && (
                    <div className="relative w-12 h-8 border border-gray-200 rounded overflow-hidden bg-white ml-auto">
                      <Image
                        src={getFileRenderUrl(school.brand.image)}
                        alt="Brand"
                        width={48}
                        height={32}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="bg-gray-50 rounded-lg p-5 border border-gray-200">
              <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                <Award size={18} className="text-gray-600" />
                <span>Overview</span>
              </h3>
              <div className="grid grid-cols-3 gap-3">
                <div className="text-center p-3 bg-white rounded-lg border border-gray-200">
                  <Users size={20} className="text-gray-600 mx-auto mb-2" />
                  <p className="text-xs text-gray-500 font-medium mb-1">Teachers</p>
                  <p className="text-lg font-bold text-gray-800">-</p>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border border-gray-200">
                  <GraduationCap size={20} className="text-gray-600 mx-auto mb-2" />
                  <p className="text-xs text-gray-500 font-medium mb-1">Students</p>
                  <p className="text-lg font-bold text-gray-800">-</p>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border border-gray-200">
                  <BookOpen size={20} className="text-gray-600 mx-auto mb-2" />
                  <p className="text-xs text-gray-500 font-medium mb-1">Worksheets</p>
                  <p className="text-lg font-bold text-gray-800">-</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};
