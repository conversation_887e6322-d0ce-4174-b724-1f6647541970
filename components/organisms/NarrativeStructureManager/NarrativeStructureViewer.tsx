'use client';

import React, { useState, useEffect } from 'react';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { getNarrativeStructureAction, extractNarrativeStructureAction } from '@/actions/narrativeStructure.action';
import { Calendar, FileText, User, Loader2, CheckCircle, AlertTriangle, Clock } from 'lucide-react';
import { NarrativeStructureDialog } from './NarrativeStructureDialog';

interface NarrativeStructureViewerProps {
  schoolId: string;
  schoolName?: string;
  refreshTrigger?: number;
  className?: string;
  metadata?: {
    createdAt?: string;
    extractedBy?: string;
    status?: string;
  };
  onDeleteClick?: () => void;
  onExtractSuccess?: () => void;
  onExtractError?: (message: string) => void;
}

export const NarrativeStructureViewer: React.FC<NarrativeStructureViewerProps> = ({
  schoolId,
  schoolName,
  refreshTrigger = 0,
  className,
  metadata,
  onDeleteClick,
  onExtractSuccess,
  onExtractError,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [structureData, setStructureData] = useState<{
    id?: string;
    content?: string;
    status?: string;
    createdAt?: string;
    updatedAt?: string;
    extractedBy?: string;
  } | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const fetchStructure = async () => {
    if (!schoolId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await getNarrativeStructureAction(schoolId);
      
      if (response.status === 'success') {
        setStructureData(response.data);
      } else {
        // If the error is "not found", we don't treat it as an error, just set data to null
        if (typeof response.message === 'string' && response.message.includes('not found')) {
          setStructureData(null);
        } else {
          setError(typeof response.message === 'string' ? response.message : 'Failed to load narrative structure');
          setStructureData(null);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      setStructureData(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStructure();
  }, [schoolId, refreshTrigger]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const openDialog = () => {
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const handleExtract = async () => {
    if (!schoolId) return;
    
    setIsExtracting(true);
    setError(null);
    
    try {
      const response = await extractNarrativeStructureAction(schoolId);
      
      if (response.status === 'success') {
        if (onExtractSuccess) {
          onExtractSuccess();
        }
        // Refresh the data to show the new status
        fetchStructure();
      } else {
        const errorMessage = typeof response.message === 'string' ? response.message : 'Failed to extract narrative structure';
        setError(errorMessage);
        if (onExtractError) {
          onExtractError(errorMessage);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      if (onExtractError) {
        onExtractError(errorMessage);
      }
    } finally {
      setIsExtracting(false);
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'PENDING':
      case 'PROCESSING':
        return <Clock size={16} className="text-amber-500" />;
      case 'FAILED':
        return <AlertTriangle size={16} className="text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'COMPLETED':
        return <span className="text-green-600 text-xs font-medium">Extraction completed</span>;
      case 'PENDING':
        return <span className="text-amber-600 text-xs font-medium">Extraction pending</span>;
      case 'PROCESSING':
        return <span className="text-amber-600 text-xs font-medium">Extraction in progress</span>;
      case 'FAILED':
        return <span className="text-red-600 text-xs font-medium">Extraction failed</span>;
      default:
        return null;
    }
  };

  return (
    <>
      <div className={`${className || ''}`} role="region" aria-label="Narrative Structure Viewer">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
          {/* Content */}
          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <Loader2 size={32} className="animate-spin text-blue-600" />
            </div>
          ) : !structureData || error ? (
            <div className="flex flex-col items-center justify-center p-12 text-center">
              <div className="w-20 h-24 bg-gray-50 border border-gray-200 rounded-md flex items-center justify-center mb-4">
                <FileText size={32} className="text-gray-300" />
              </div>
              <p className="text-gray-700 font-medium mb-2">No narrative structure available</p>
              <p className="text-sm text-gray-500 mb-4">Extract a narrative structure for this school</p>
              <Button
                variant="primary"
                onClick={handleExtract}
                isLoading={isExtracting}
                iconProps={{
                  variant: 'download',
                  className: 'w-4'
                }}
                className="mt-2 w-fit"
              >
                {isExtracting ? 'Extracting...' : 'Extract Structure'}
              </Button>
            </div>
          ) : (
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                {/* Preview Thumbnail */}
                <div 
                  className="w-full md:w-1/3 bg-gray-50 p-6 rounded-lg border border-gray-100 flex items-center justify-center cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  onClick={openDialog}
                  role="button"
                  aria-label="View narrative structure"
                  tabIndex={0}
                >
                  <div className="relative w-full h-full min-h-[160px] flex items-center justify-center">
                    <div className="w-24 h-32 bg-white border border-gray-200 rounded-md shadow-sm flex items-center justify-center">
                      <FileText size={32} className="text-blue-500" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 transform translate-x-1/4 translate-y-1/4">
                      {structureData.status === 'COMPLETED' && (
                        <div className="text-green-500 bg-white p-1 rounded-full border border-green-100 shadow-sm">
                          <CheckCircle size={16} />
                        </div>
                      )}
                      {(structureData.status === 'PENDING' || structureData.status === 'PROCESSING') && (
                        <div className="text-amber-500 bg-white p-1 rounded-full border border-amber-100 shadow-sm">
                          <Clock size={16} />
                        </div>
                      )}
                      {structureData.status === 'FAILED' && (
                        <div className="text-red-500 bg-white p-1 rounded-full border border-red-100 shadow-sm">
                          <AlertTriangle size={16} />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Structure Details */}
                <div className="w-full md:w-2/3">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="font-medium text-gray-800">{schoolName ? `${schoolName} Narrative Structure` : 'Narrative Structure'}</h4>
                      {getStatusText(structureData.status)}
                    </div>
                    <div className="flex space-x-2">
                      {structureData.status === 'COMPLETED' && (
                        <Button
                          variant="outline"
                          onClick={openDialog}
                          iconProps={{
                            variant: 'eye',
                            className:'w-5'
                          }}
                          className="!w-auto text-xs"
                        >
                          View
                        </Button>
                      )}
                      <Button
                        variant="error"
                        onClick={onDeleteClick}
                        iconProps={{
                          variant: 'trash-2',
                           className:'w-4'
                        }}
                        className="!w-auto text-white text-xs"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                    {structureData.createdAt && (
                      <div className="flex items-center">
                        <Calendar className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">Created:</span>{' '}
                          <span className="text-gray-700">{formatDate(structureData.createdAt)}</span>
                        </div>
                      </div>
                    )}
                    {structureData.extractedBy && (
                      <div className="flex items-center">
                        <User className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">By:</span>{' '}
                          <span className="text-gray-700">{structureData.extractedBy}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {(structureData.status === 'FAILED' || structureData.status === 'PENDING' || structureData.status === 'PROCESSING') && (
                    <Button
                      variant="ghost"
                      onClick={handleExtract}
                      isLoading={isExtracting}
                      iconProps={{
                        variant: 'refresh-ccw',
                        className:'w-4'
                      }}
                      className="text-blue-600 w-fit hover:text-blue-800 hover:bg-blue-50 text-sm font-medium"
                    >
                      {isExtracting ? 'Extracting...' : 'Re-extract Structure'}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Structure Dialog */}
      <NarrativeStructureDialog
        isOpen={isDialogOpen}
        onClose={closeDialog}
        structure={structureData?.content || ''}
      />
    </>
  );
};