'use client';

import React, { useState } from 'react';
import { NarrativeStructureViewer } from './NarrativeStructureViewer';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { DeleteNarrativeStructureModal } from './DeleteNarrativeStructureModal';

interface NarrativeStructureManagerProps {
  schoolId?: string;
  schoolName?: string;
  onDelete?: () => Promise<void>;
  className?: string;
  metadata?: {
    createdAt?: string;
    extractedBy?: string;
    status?: string;
  };
  refreshTrigger?: number;
}

export const NarrativeStructureManager: React.FC<NarrativeStructureManagerProps> = ({
  schoolId,
  schoolName,
  onDelete,
  className,
  metadata,
  refreshTrigger,
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteSuccess = () => {
    setSuccess('Narrative structure deleted successfully');
  };

  const handleExtractSuccess = () => {
    setSuccess('Narrative structure extraction initiated successfully');
  };

  const handleExtractError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <div className={`${className || ''}`} role="region" aria-label="Narrative Structure Manager">
      {/* Error/Success Messages */}
      {error && (
        <AlertMessage 
          type="error" 
          message={error} 
          aria-live="assertive"
        />
      )}
      {success && (
        <AlertMessage 
          type="success" 
          message={success} 
          aria-live="polite"
        />
      )}

      {/* Narrative Structure Viewer */}
      <div className="mb-6">
        <NarrativeStructureViewer
          schoolId={schoolId || ''}
          schoolName={schoolName}
          onDeleteClick={handleDeleteClick}
          onExtractSuccess={handleExtractSuccess}
          onExtractError={handleExtractError}
          metadata={metadata}
          refreshTrigger={refreshTrigger}
        />
      </div>

      {/* Delete Modal */}
      <DeleteNarrativeStructureModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={onDelete}
        onSuccess={handleDeleteSuccess}
        schoolName={schoolName}
      />
    </div>
  );
};