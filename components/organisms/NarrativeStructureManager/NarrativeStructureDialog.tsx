'use client';

import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface NarrativeStructureDialogProps {
  isOpen: boolean;
  onClose: () => void;
  structure: string;
}

export const NarrativeStructureDialog: React.FC<NarrativeStructureDialogProps> = ({
  isOpen,
  onClose,
  structure,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-bold">Narrative Structure</h2>
          <button 
            onClick={onClose}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
            aria-label="Close"
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {structure ? (
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg border border-gray-200 text-sm">
                {structure}
              </pre>
            </div>
          ) : (
            <div className="text-center p-8 text-gray-500">
              No narrative structure content available.
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 flex justify-end">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-4 py-2"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};