'use client';

import React, { useState } from 'react';
import { AlertCircle, CheckCircle2, Trash2, X } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface DeleteNarrativeStructureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => Promise<void>;
  onSuccess?: () => void;
  schoolName: string;
}

export const DeleteNarrativeStructureModal: React.FC<DeleteNarrativeStructureModalProps> = ({
  isOpen,
  onClose,
  onDelete,
  onSuccess,
  schoolName,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await onDelete();
      setSuccess('Narrative structure deleted successfully');
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
      
      // Close the modal after a short delay
      setTimeout(() => {
        onClose();
        setIsSubmitting(false);
        setSuccess(null);
      }, 1500);
    } catch (error) {
      setError('Failed to delete narrative structure');
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn" style={{ zIndex: 9999 }}>
      <div className="bg-white rounded-xl shadow-2xl p-0 w-full max-w-md overflow-hidden transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
          <h2 className="text-lg font-bold flex items-center gap-2">
            <Trash2 size={18} />
            Delete Narrative Structure
          </h2>
          <button 
            onClick={onClose}
            className="rounded-full p-1.5 bg-white/20 hover:bg-white/30 transition-colors duration-200"
            aria-label="Close"
            disabled={isSubmitting}
          >
            <X size={16} className="text-white" />
          </button>
        </div>

        <div className="p-6">
          {/* Status Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md flex items-center gap-2">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md flex items-center gap-2">
              <CheckCircle2 size={16} />
              <span>{success}</span>
            </div>
          )}

          {/* Warning Message */}
          <div className="mb-4 p-3 bg-amber-50 text-amber-700 rounded-md flex items-start gap-2">
            <AlertCircle size={16} className="mt-0.5 flex-shrink-0" />
            <span>
              Are you sure you want to delete the narrative structure for <strong>{schoolName}</strong>? This action cannot be undone.
            </span>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isSubmitting}
              className="px-4 py-2 flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 size={16} />
                  Delete
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};