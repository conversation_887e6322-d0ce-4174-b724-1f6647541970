'use client';

import React from 'react';
import { TTableCommonClientSort } from './CustomTable.types';

// Basic implementation of CustomTableClientSort
const TableCommonClientSort = <T,>({
  isLoading,
  wrapperClass,
  theadClass,
  tbodyClass,
  tableHeight,
  columns,
  tableData,
  handleClickTableRow,
  isShowPagination,
  ...props
}: TTableCommonClientSort<T> & { tableConfigName?: string }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto ${wrapperClass || ''}`}>
      <table className="table w-full">
        <thead className={theadClass}>
          <tr>
            {columns.map((column, index) => (
              <th key={index} className="text-left">
                {typeof column.header === 'function' 
                  ? column.header({} as any) 
                  : column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className={tbodyClass}>
          {tableData.map((row, rowIndex) => (
            <tr 
              key={rowIndex}
              className="hover:bg-gray-50 cursor-pointer"
              onClick={() => handleClickTableRow?.({ original: row } as any)}
            >
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="py-2 px-4">
                  {typeof column.cell === 'function'
                    ? column.cell({ getValue: () => (row as any)[column.accessorKey || ''], row: { original: row } } as any)
                    : (row as any)[column.accessorKey || '']}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      
      {isShowPagination && (
        <div className="flex justify-center mt-4">
          <div className="text-sm text-gray-500">
            Showing {tableData.length} items
          </div>
        </div>
      )}
    </div>
  );
};

export default TableCommonClientSort;
